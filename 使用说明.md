# DNS中继服务器使用说明

## 快速开始

### 1. 环境要求
- **Java**: JDK 8或更高版本
- **Maven**: 3.6+（用于编译）
- **操作系统**: Windows/Linux/macOS
- **权限**: 管理员/root权限（绑定53端口需要）

### 2. 编译项目
```bash
# 编译源代码
mvn clean compile

# 打包成可执行JAR
mvn clean package
```

### 3. 运行程序

#### Windows系统
```cmd
# 以管理员身份运行命令提示符，然后执行：

# 基本运行（使用默认配置）
java -jar target\dns-relay.jar

# 调试模式运行
java -jar target\dns-relay.jar -d

# 指定上游DNS和配置文件
java -jar target\dns-relay.jar -d ******* config\dnsrelay.txt

# 或者直接运行测试脚本
test_dns_relay.bat
```

#### Linux/macOS系统
```bash
# 使用sudo获取权限，然后执行：

# 基本运行
sudo java -jar target/dns-relay.jar

# 调试模式运行
sudo java -jar target/dns-relay.jar -d

# 指定上游DNS和配置文件
sudo java -jar target/dns-relay.jar -d ******* config/dnsrelay.txt

# 或者运行测试脚本
chmod +x test_dns_relay.sh
sudo ./test_dns_relay.sh
```

## 配置文件说明

### 配置文件格式
配置文件 `config/dnsrelay.txt` 的格式为：
```
IP地址 域名
```

### 配置示例
```
# 注释行以#开头
# 拦截域名（IP设为0.0.0.0）
0.0.0.0 blocked.example.com
0.0.0.0 ads.example.com

# 本地解析（指定具体IP）
************* local.example.com
127.0.0.1 test.local

# 内网服务器
************ mail.company.com
```

### 三种处理模式
1. **域名拦截**: IP为0.0.0.0时，返回NXDOMAIN错误
2. **本地解析**: IP为有效地址时，直接返回该IP
3. **中继转发**: 域名不在配置中时，转发给上游DNS服务器

## 命令行参数

### 语法
```
java -jar dns-relay.jar [-d | -dd] [dns-server-ipaddr] [filename]
```

### 参数说明
- `-d`: 启用基本调试模式（显示查询域名和处理结果）
- `-dd`: 启用详细调试模式（显示完整的报文信息）
- `dns-server-ipaddr`: 上游DNS服务器IP地址（默认：*******）
- `filename`: 配置文件路径（默认：dnsrelay.txt）

### 使用示例
```bash
# 显示帮助信息
java -jar dns-relay.jar -h

# 使用默认配置
java -jar dns-relay.jar

# 使用学校DNS服务器
java -jar dns-relay.jar ********

# 启用调试并指定配置文件
java -jar dns-relay.jar -d ******* config/dnsrelay.txt

# 详细调试模式
java -jar dns-relay.jar -dd *************** config/dnsrelay.txt
```

## 测试方法

### 1. 配置系统DNS
在测试前，需要将系统DNS设置为127.0.0.1：

#### Windows系统
1. 打开"网络和共享中心"
2. 点击"更改适配器设置"
3. 右键点击网络连接，选择"属性"
4. 选择"Internet协议版本4(TCP/IPv4)"，点击"属性"
5. 选择"使用下面的DNS服务器地址"
6. 首选DNS服务器设置为：127.0.0.1
7. 点击"确定"保存设置

#### Linux系统
```bash
# 临时修改DNS设置
echo "nameserver 127.0.0.1" | sudo tee /etc/resolv.conf

# 或者编辑网络配置文件
sudo nano /etc/systemd/resolved.conf
# 添加：DNS=127.0.0.1
```

### 2. 启动DNS中继服务器
```bash
# Windows（管理员权限）
java -jar target\dns-relay.jar -d ******* config\dnsrelay.txt

# Linux/macOS（root权限）
sudo java -jar target/dns-relay.jar -d ******* config/dnsrelay.txt
```

### 3. 测试DNS查询

#### 测试域名拦截
```cmd
nslookup blocked.example.com
# 期望结果：返回"域名不存在"错误
```

#### 测试本地解析
```cmd
nslookup local.example.com
# 期望结果：返回*************
```

#### 测试中继转发
```cmd
nslookup www.baidu.com
# 期望结果：返回百度的真实IP地址
```

### 4. 使用ping命令测试
```cmd
ping test.local
# 如果配置正确，应该能ping通127.0.0.1

ping blocked.example.com
# 应该显示无法解析域名
```

## 故障排除

### 常见问题

#### 1. 权限错误
**错误信息**: `Permission denied` 或 `Access denied`
**解决方案**: 
- Windows: 以管理员身份运行命令提示符
- Linux/macOS: 使用sudo运行程序

#### 2. 端口被占用
**错误信息**: `Address already in use` 或 `Port 53 is already in use`
**解决方案**: 
- 检查是否有其他DNS服务正在运行
- Windows: 停止DNS Client服务
- Linux: 停止systemd-resolved服务

#### 3. 配置文件未找到
**错误信息**: `Configuration file not found`
**解决方案**: 
- 检查配置文件路径是否正确
- 确保config/dnsrelay.txt文件存在

#### 4. 网络连接问题
**错误信息**: `Timeout` 或 `Network unreachable`
**解决方案**: 
- 检查网络连接
- 确认上游DNS服务器地址正确
- 检查防火墙设置

### 调试技巧

#### 1. 使用调试模式
```bash
# 基本调试信息
java -jar dns-relay.jar -d

# 详细调试信息
java -jar dns-relay.jar -dd
```

#### 2. 使用Wireshark抓包
1. 启动Wireshark
2. 选择正确的网络接口
3. 设置过滤器：`udp port 53`
4. 观察DNS查询和响应报文

#### 3. 检查日志输出
程序会在控制台输出详细的运行日志，包括：
- 配置文件加载情况
- 收到的DNS查询
- 处理结果（拦截/本地解析/转发）
- 网络通信状态

## 性能优化

### 1. 配置文件优化
- 将常用域名放在配置文件前面
- 定期清理无用的配置条目
- 使用合理的TTL值

### 2. 网络优化
- 选择响应速度快的上游DNS服务器
- 考虑使用多个上游DNS服务器做负载均衡

### 3. 系统优化
- 增加系统的UDP缓冲区大小
- 调整Java虚拟机参数

## 扩展功能

### 1. 支持IPv6
可以扩展程序支持AAAA记录查询

### 2. DNS缓存
可以添加本地DNS缓存功能提高性能

### 3. Web管理界面
可以开发Web界面来管理配置文件

### 4. 统计功能
可以添加查询统计和日志记录功能

## 技术支持

如果遇到问题，请检查：
1. Java版本是否正确
2. 是否有足够的权限
3. 配置文件格式是否正确
4. 网络连接是否正常

更多技术细节请参考项目文档中的设计文档。

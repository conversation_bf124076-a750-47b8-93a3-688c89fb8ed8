<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="com.bupt.dnsrelay.IntegrationTest" time="0.017" tests="6" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="24"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="GBK"/>
    <property name="java.class.path" value="F:\Desk top\DNS_modify\target\test-classes;F:\Desk top\DNS_modify\target\classes;D:\APP\App_code\Java\Apache\MAVEN—local repository\junit\junit\4.13.2\junit-4.13.2.jar;D:\APP\App_code\Java\Apache\MAVEN—local repository\org\hamcrest\hamcrest-core\1.3\hamcrest-core-1.3.jar;D:\APP\App_code\Java\Apache\MAVEN—local repository\org\slf4j\slf4j-api\1.7.36\slf4j-api-1.7.36.jar;D:\APP\App_code\Java\Apache\MAVEN—local repository\org\slf4j\slf4j-simple\1.7.36\slf4j-simple-1.7.36.jar;"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="https://java.oracle.com/"/>
    <property name="user.timezone" value="Asia/Shanghai"/>
    <property name="os.name" value="Windows 11"/>
    <property name="java.vm.specification.version" value="24"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="CN"/>
    <property name="sun.boot.library.path" value="D:\APP\App_code\Java\jdk-24\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire10298443176430687970\surefirebooter-20250702183632761_3.jar C:\Users\<USER>\AppData\Local\Temp\surefire10298443176430687970 2025-07-02T18-36-32_571-jvmRun1 surefire-20250702183632761_1tmp surefire_0-20250702183632761_2tmp"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="F:\Desk top\DNS_modify\target\test-classes;F:\Desk top\DNS_modify\target\classes;D:\APP\App_code\Java\Apache\MAVEN—local repository\junit\junit\4.13.2\junit-4.13.2.jar;D:\APP\App_code\Java\Apache\MAVEN—local repository\org\hamcrest\hamcrest-core\1.3\hamcrest-core-1.3.jar;D:\APP\App_code\Java\Apache\MAVEN—local repository\org\slf4j\slf4j-api\1.7.36\slf4j-api-1.7.36.jar;D:\APP\App_code\Java\Apache\MAVEN—local repository\org\slf4j\slf4j-simple\1.7.36\slf4j-simple-1.7.36.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\APP\App_code\Java\jdk-24"/>
    <property name="file.separator" value="\"/>
    <property name="basedir" value="F:\Desk top\DNS_modify"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire10298443176430687970\surefirebooter-20250702183632761_3.jar"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="24.0.1*****"/>
    <property name="user.name" value="Xuan"/>
    <property name="stdout.encoding" value="GBK"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="D:\APP\App_code\Java\Apache\MAVEN—local repository"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="java.version" value="24.0.1"/>
    <property name="user.dir" value="F:\Desk top\DNS_modify"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="native.encoding" value="GBK"/>
    <property name="java.library.path" value="D:\APP\App_code\Java\jdk-24\bin;C:\WINDOWS\Sun\Java\bin;C:\WINDOWS\system32;C:\WINDOWS;D:\APP\App_code\Anaconda3;D:\APP\App_code\Anaconda3\Library\mingw-w64\bin;D:\APP\App_code\Anaconda3\Library\usr\bin;D:\APP\App_code\Anaconda3\Library\bin;D:\APP\App_code\Anaconda3\Scripts;D:\APP\App_code\Anaconda3\bin;D:\APP\App_code\Anaconda3\condabin;F:\VMware\VMware Workstation\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0;C:\WINDOWS\System32\OpenSSH;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Recovery\OEM\Backup;C:\Program Files\dotnet;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA app\NvDLISR;C:\Recovery\OEM\Backup;D:\APP\App_code\Java\jdk-24\bin;D:\APP\App_code\Java\Apache\apache-maven-3.9.9\bin;D:\APP\App_code\Git\cmd;C:\Users\<USER>\AppData\Roaming\Python\Python312\Scripts;D:\APP\App_code\Anaconda3;D:\APP\App_code\Anaconda3\Scripts;D:\APP\App_code\Anaconda3\Library\bin;D:\APP\App_code\Anaconda3\Library\usr\bin;D:\APP\App_code\Anaconda3\Library\mingw-w64\bin;D:\APP\App_code\nodejs;D:\APP\App_code\nodejs;D:\APP\App_code\nodejs\node_global;D:\APP\App_code\nodejs\node_cache;D:\APP\App_code\MATLAB\R2025a\bin;D:\APP\App_code\mingw64\bin;d:\APP\App_code\IDE\Trae\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\APP\App_code\IDE\Microsoft VS Code\bin;D:\APP\App_code\Java\Apache\apache-maven-3.9.9\bin;D:\APP\App_code\IDE\IntelliJ IDEA 2025.1\bin;.;D:\APP\App_code\IDE\cursor\resources\app\bin;C:\Users\<USER>\AppData\Roaming\Python\Python312\Scripts;D:\APP\App_code\nodejs;D:\APP\App_code\nodejs\node_global;D:\APP\App_code\nodejs\node_cache;.;."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="stderr.encoding" value="GBK"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="24.0.1*****"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="68.0"/>
  </properties>
  <testcase name="testLocalResolutionWorkflow" classname="com.bupt.dnsrelay.IntegrationTest" time="0.001">
    <system-out><![CDATA[Loading DNS configuration from: C:\Users\<USER>\AppData\Local\Temp\integration_test_config8137866650073594883.txt
Loaded: blocked.test.com -> 0.0.0.0 (BLOCKED)
Loaded: ads.malware.com -> 0.0.0.0 (BLOCKED)
Loaded: local.test.com -> *************
Loaded: localhost.test.com -> 127.0.0.1
Loaded: server.internal.com -> ********
Loaded 5 domain entries from configuration file
]]></system-out>
  </testcase>
  <testcase name="testRelayForwardingWorkflow" classname="com.bupt.dnsrelay.IntegrationTest" time="0.003">
    <system-out><![CDATA[Loading DNS configuration from: C:\Users\<USER>\AppData\Local\Temp\integration_test_config5009058006020377388.txt
Loaded: blocked.test.com -> 0.0.0.0 (BLOCKED)
Loaded: ads.malware.com -> 0.0.0.0 (BLOCKED)
Loaded: local.test.com -> *************
Loaded: localhost.test.com -> 127.0.0.1
Loaded: server.internal.com -> ********
Loaded 5 domain entries from configuration file
]]></system-out>
  </testcase>
  <testcase name="testDNSMessageAndRecordIntegration" classname="com.bupt.dnsrelay.IntegrationTest" time="0.003">
    <system-out><![CDATA[Loading DNS configuration from: C:\Users\<USER>\AppData\Local\Temp\integration_test_config1133372718657330970.txt
Loaded: blocked.test.com -> 0.0.0.0 (BLOCKED)
Loaded: ads.malware.com -> 0.0.0.0 (BLOCKED)
Loaded: local.test.com -> *************
Loaded: localhost.test.com -> 127.0.0.1
Loaded: server.internal.com -> ********
Loaded 5 domain entries from configuration file
]]></system-out>
  </testcase>
  <testcase name="testCaseInsensitiveIntegration" classname="com.bupt.dnsrelay.IntegrationTest" time="0.005">
    <system-out><![CDATA[Loading DNS configuration from: C:\Users\<USER>\AppData\Local\Temp\integration_test_config10583404717862776131.txt
Loaded: blocked.test.com -> 0.0.0.0 (BLOCKED)
Loaded: ads.malware.com -> 0.0.0.0 (BLOCKED)
Loaded: local.test.com -> *************
Loaded: localhost.test.com -> 127.0.0.1
Loaded: server.internal.com -> ********
Loaded 5 domain entries from configuration file
]]></system-out>
  </testcase>
  <testcase name="testMultipleDomainTypes" classname="com.bupt.dnsrelay.IntegrationTest" time="0.002">
    <system-out><![CDATA[Loading DNS configuration from: C:\Users\<USER>\AppData\Local\Temp\integration_test_config5417680856791876823.txt
Loaded: blocked.test.com -> 0.0.0.0 (BLOCKED)
Loaded: ads.malware.com -> 0.0.0.0 (BLOCKED)
Loaded: local.test.com -> *************
Loaded: localhost.test.com -> 127.0.0.1
Loaded: server.internal.com -> ********
Loaded 5 domain entries from configuration file
]]></system-out>
  </testcase>
  <testcase name="testDomainBlockingWorkflow" classname="com.bupt.dnsrelay.IntegrationTest" time="0.003">
    <system-out><![CDATA[Loading DNS configuration from: C:\Users\<USER>\AppData\Local\Temp\integration_test_config6010901439111498838.txt
Loaded: blocked.test.com -> 0.0.0.0 (BLOCKED)
Loaded: ads.malware.com -> 0.0.0.0 (BLOCKED)
Loaded: local.test.com -> *************
Loaded: localhost.test.com -> 127.0.0.1
Loaded: server.internal.com -> ********
Loaded 5 domain entries from configuration file
]]></system-out>
  </testcase>
</testsuite>
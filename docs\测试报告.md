# DNS中继服务器测试报告

## 1. 测试环境

### 1.1 硬件环境
- **操作系统**: Windows 10 / Ubuntu 20.04
- **Java版本**: OpenJDK 8+
- **内存**: 4GB+
- **网络**: 有线/无线网络连接

### 1.2 软件环境
- **开发工具**: IntelliJ IDEA / Eclipse
- **构建工具**: Maven 3.6+
- **测试工具**: nslookup, ping, Wireshark

### 1.3 网络配置
- **原始DNS**: 自动获取（如*********）
- **测试DNS**: 127.0.0.1（本机）
- **上游DNS**: *******（Google DNS）

## 2. 测试准备

### 2.1 编译项目
```bash
mvn clean compile
mvn package
```

### 2.2 配置文件准备
创建测试配置文件 `config/dnsrelay.txt`：
```
# 测试配置
0.0.0.0 blocked.test.com
************* local.test.com
127.0.0.1 localhost.test.com
```

### 2.3 系统DNS配置
1. 记录原始DNS设置
2. 修改系统DNS为127.0.0.1
3. 验证DNS设置生效

## 3. 功能测试

### 3.1 域名拦截功能测试

#### 3.1.1 测试用例1：拦截配置域名
- **测试命令**: `nslookup blocked.test.com`
- **期望结果**: 返回NXDOMAIN错误
- **实际结果**: 
  ```
  服务器:  localhost
  Address:  127.0.0.1
  
  *** localhost 找不到 blocked.test.com: Non-existent domain
  ```
- **测试状态**: ✅ 通过

#### 3.1.2 测试用例2：多个拦截域名
- **测试命令**: 
  ```bash
  nslookup ads.example.com
  nslookup malware.example.com
  ```
- **期望结果**: 均返回NXDOMAIN错误
- **实际结果**: [待填写]
- **测试状态**: [待测试]

### 3.2 本地解析功能测试

#### 3.2.1 测试用例3：本地域名解析
- **测试命令**: `nslookup local.test.com`
- **期望结果**: 返回*************
- **实际结果**: 
  ```
  服务器:  localhost
  Address:  127.0.0.1
  
  名称:    local.test.com
  Address:  *************
  ```
- **测试状态**: ✅ 通过

#### 3.2.2 测试用例4：localhost解析
- **测试命令**: `nslookup localhost.test.com`
- **期望结果**: 返回127.0.0.1
- **实际结果**: [待填写]
- **测试状态**: [待测试]

### 3.3 中继转发功能测试

#### 3.3.1 测试用例5：公网域名解析
- **测试命令**: `nslookup www.baidu.com`
- **期望结果**: 返回百度的真实IP地址
- **实际结果**: 
  ```
  服务器:  localhost
  Address:  127.0.0.1
  
  非权威应答:
  名称:    www.baidu.com
  Address:  **************
  ```
- **测试状态**: ✅ 通过

#### 3.3.2 测试用例6：国外网站解析
- **测试命令**: `nslookup www.google.com`
- **期望结果**: 返回Google的真实IP地址
- **实际结果**: [待填写]
- **测试状态**: [待测试]

## 4. 性能测试

### 4.1 响应时间测试
- **测试方法**: 使用ping命令测试响应时间
- **测试结果**:
  ```bash
  # 本地解析响应时间
  ping local.test.com
  # 平均响应时间: [待填写]ms
  
  # 转发解析响应时间
  ping www.baidu.com
  # 平均响应时间: [待填写]ms
  ```

### 4.2 并发测试
- **测试方法**: 同时发起多个DNS查询
- **测试工具**: 自定义脚本或专业工具
- **测试结果**: [待填写]

## 5. 异常测试

### 5.1 网络异常测试

#### 5.1.1 上游DNS不可达
- **测试方法**: 配置无效的上游DNS地址
- **期望结果**: 超时后返回错误
- **实际结果**: [待填写]

#### 5.1.2 网络断开测试
- **测试方法**: 断开网络连接后进行查询
- **期望结果**: 本地解析正常，转发失败
- **实际结果**: [待填写]

### 5.2 配置文件异常测试

#### 5.2.1 配置文件不存在
- **测试方法**: 删除配置文件后启动程序
- **期望结果**: 程序正常启动，使用空配置
- **实际结果**: [待填写]

#### 5.2.2 配置文件格式错误
- **测试方法**: 在配置文件中添加格式错误的行
- **期望结果**: 跳过错误行，其他配置正常
- **实际结果**: [待填写]

## 6. 调试功能测试

### 6.1 基本调试模式(-d)
- **测试命令**: `java -jar dns-relay.jar -d`
- **期望结果**: 显示查询域名和处理结果
- **实际结果**: [待填写]

### 6.2 详细调试模式(-dd)
- **测试命令**: `java -jar dns-relay.jar -dd`
- **期望结果**: 显示详细的报文信息和网络通信
- **实际结果**: [待填写]

## 7. 兼容性测试

### 7.1 操作系统兼容性
- **Windows 10**: [测试状态]
- **Ubuntu 20.04**: [测试状态]
- **macOS**: [测试状态]

### 7.2 Java版本兼容性
- **Java 8**: [测试状态]
- **Java 11**: [测试状态]
- **Java 17**: [测试状态]

## 8. 安全测试

### 8.1 权限测试
- **测试内容**: 验证程序需要的最小权限
- **测试结果**: [待填写]

### 8.2 输入验证测试
- **测试内容**: 发送恶意构造的DNS查询
- **测试结果**: [待填写]

## 9. 测试总结

### 9.1 测试统计
- **总测试用例**: 15个
- **通过用例**: [待统计]个
- **失败用例**: [待统计]个
- **通过率**: [待计算]%

### 9.2 发现的问题
1. [问题描述1]
   - **严重程度**: 高/中/低
   - **解决方案**: [解决方案]

2. [问题描述2]
   - **严重程度**: 高/中/低
   - **解决方案**: [解决方案]

### 9.3 改进建议
1. [改进建议1]
2. [改进建议2]
3. [改进建议3]

### 9.4 测试结论
[根据测试结果给出总体评价]

## 10. 附录

### 10.1 测试数据
[详细的测试数据和日志]

### 10.2 Wireshark抓包分析
[网络报文分析截图和说明]

### 10.3 测试脚本
[自动化测试脚本代码]

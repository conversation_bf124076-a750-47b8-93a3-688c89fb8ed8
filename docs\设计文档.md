# DNS中继服务器设计文档

## 1. 项目概述

### 1.1 项目背景
本项目是北京邮电大学（BUPT）与伦敦玛丽女王大学（QMUL）合作办学项目"通信与网络"课程的大作业，要求实现一个DNS中继服务器（DNS Relay）。

### 1.2 项目目标
- 实现DNS协议的基本功能
- 支持域名拦截、本地解析和中继转发三种核心功能
- 深入理解DNS协议工作原理和报文格式
- 掌握网络Socket编程技术

## 2. 需求分析

### 2.1 功能需求

#### 2.1.1 域名拦截（情况一）
- **输入**：DNS查询请求，查询域名在配置文件中且IP为0.0.0.0
- **处理**：不转发查询，构造NXDOMAIN错误响应
- **输出**：返回"域名不存在"错误响应

#### 2.1.2 本地解析（情况二）
- **输入**：DNS查询请求，查询域名在配置文件中且IP为有效地址
- **处理**：不转发查询，构造包含配置IP的响应
- **输出**：返回包含本地配置IP地址的响应

#### 2.1.3 中继转发（情况三）
- **输入**：DNS查询请求，查询域名不在配置文件中
- **处理**：转发查询到上游DNS服务器，等待响应
- **输出**：将上游服务器的响应转发给客户端

### 2.2 非功能需求
- **性能**：支持并发查询处理
- **可靠性**：网络异常时的错误处理
- **可维护性**：模块化设计，代码清晰
- **可扩展性**：支持配置文件动态加载

## 3. 系统架构设计

### 3.1 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   DNS客户端     │    │  DNS中继服务器   │    │  上游DNS服务器   │
│                 │    │                 │    │                 │
│  ┌───────────┐  │    │  ┌───────────┐  │    │  ┌───────────┐  │
│  │nslookup等 │  │◄──►│  │UDPServer  │  │◄──►│  │真实DNS    │  │
│  └───────────┘  │    │  └───────────┘  │    │  │服务器     │  │
└─────────────────┘    │  ┌───────────┐  │    │  └───────────┘  │
                       │  │ConfigParser│ │    └─────────────────┘
                       │  └───────────┘  │
                       │  ┌───────────┐  │
                       │  │DNSParser  │  │
                       │  └───────────┘  │
                       └─────────────────┘
```

### 3.2 模块划分

#### 3.2.1 网络通信模块（UDPServer）
- **职责**：UDP Socket通信，接收查询，发送响应
- **主要方法**：
  - `start()`: 启动服务器
  - `receiveQuery()`: 接收DNS查询
  - `sendResponse()`: 发送DNS响应
  - `forwardQuery()`: 转发查询到上游服务器

#### 3.2.2 配置解析模块（ConfigParser）
- **职责**：读取和解析配置文件
- **主要方法**：
  - `loadConfig()`: 加载配置文件
  - `lookupDomain()`: 查找域名对应IP
  - `isDomainBlocked()`: 检查域名是否被拦截

#### 3.2.3 DNS解析模块（DNSParser）
- **职责**：DNS报文的解析和构造
- **主要方法**：
  - `parseMessage()`: 解析DNS报文
  - `buildMessage()`: 构造DNS报文
  - `extractDomain()`: 提取域名

#### 3.2.4 主控制模块（DNSRelayServer）
- **职责**：程序入口，协调各模块工作
- **主要方法**：
  - `main()`: 程序入口点
  - `handleDNSQuery()`: 处理DNS查询的核心逻辑

## 4. 详细设计

### 4.1 DNS报文格式处理

#### 4.1.1 DNS报文头结构（12字节）
```
 0  1  2  3  4  5  6  7  8  9  A  B  C  D  E  F
+--+--+--+--+--+--+--+--+--+--+--+--+--+--+--+--+
|                      ID                       |
+--+--+--+--+--+--+--+--+--+--+--+--+--+--+--+--+
|QR|   Opcode  |AA|TC|RD|RA|   Z    |   RCODE   |
+--+--+--+--+--+--+--+--+--+--+--+--+--+--+--+--+
|                    QDCOUNT                    |
+--+--+--+--+--+--+--+--+--+--+--+--+--+--+--+--+
|                    ANCOUNT                    |
+--+--+--+--+--+--+--+--+--+--+--+--+--+--+--+--+
|                    NSCOUNT                    |
+--+--+--+--+--+--+--+--+--+--+--+--+--+--+--+--+
|                    ARCOUNT                    |
+--+--+--+--+--+--+--+--+--+--+--+--+--+--+--+--+
```

#### 4.1.2 关键标志位
- **QR**: 0=查询，1=响应
- **AA**: 权威答案标志
- **RD**: 期望递归标志
- **RCODE**: 响应码（0=成功，3=域名不存在）

### 4.2 核心算法流程

#### 4.2.1 主服务循环
```java
while (isRunning) {
    packet = udpServer.receiveQuery();
    if (packet != null) {
        response = handleDNSQuery(packet.getData());
        udpServer.sendResponse(response, packet.getClientAddress(), packet.getClientPort());
    }
}
```

#### 4.2.2 DNS查询处理逻辑
```java
String domain = extractDomainFromQuery(queryData);

if (configParser.isDomainBlocked(domain)) {
    return createErrorResponse(queryData, NXDOMAIN);
} else if (configParser.isLocalDomain(domain)) {
    String ip = configParser.lookupDomain(domain);
    return createLocalResponse(queryData, ip);
} else {
    return udpServer.forwardQuery(queryData, upstreamDNS);
}
```

## 5. 实现细节

### 5.1 配置文件格式
```
# 注释行以#开头
IP地址 域名

# 示例：
0.0.0.0 blocked.example.com    # 拦截域名
************* local.example.com # 本地解析
```

### 5.2 错误处理
- **网络异常**：超时重试，记录错误日志
- **配置错误**：验证IP和域名格式，跳过无效条目
- **DNS格式错误**：返回格式错误响应

### 5.3 调试功能
- **-d**: 基本调试信息（查询域名、处理结果）
- **-dd**: 详细调试信息（报文内容、网络通信）

## 6. 测试设计

### 6.1 测试环境配置
1. 修改系统DNS设置为127.0.0.1
2. 启动DNS中继服务器
3. 使用nslookup进行测试

### 6.2 测试用例

#### 6.2.1 域名拦截测试
- **输入**：`nslookup blocked.example.com`
- **期望**：返回"域名不存在"错误

#### 6.2.2 本地解析测试
- **输入**：`nslookup local.example.com`
- **期望**：返回配置的IP地址

#### 6.2.3 中继转发测试
- **输入**：`nslookup www.baidu.com`
- **期望**：返回真实的IP地址

## 7. 部署说明

### 7.1 编译打包
```bash
mvn clean package
```

### 7.2 运行方式
```bash
# 基本运行
java -jar target/dns-relay.jar

# 指定参数运行
java -jar target/dns-relay.jar -d ******* config/dnsrelay.txt
```

### 7.3 权限要求
- Windows：需要管理员权限绑定53端口
- Linux：需要root权限或使用sudo

## 8. 扩展功能

### 8.1 可能的改进
- 支持IPv6（AAAA记录）
- 实现DNS缓存功能
- 支持多个上游DNS服务器
- 添加Web管理界面
- 支持DNS over HTTPS (DoH)

### 8.2 性能优化
- 使用线程池处理并发请求
- 实现连接池复用
- 添加请求限流功能
